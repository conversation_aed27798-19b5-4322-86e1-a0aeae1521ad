// App.tsx (ĐÃ XỬ LÝ FULL SAFE AREA VÀ NAVIGATION)
import React, { useEffect, useState } from 'react';
import {
  View, Text, StyleSheet, Image, Alert, Platform, PermissionsAndroid
} from 'react-native';
import messaging from '@react-native-firebase/messaging';
import { DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-toast-message';
import { navigationRef, navigate } from './NavigationService';
import io from 'socket.io-client';
import Tts from 'react-native-tts';
import { TtsProvider, useTts } from './contexts/TtsContext';
import { NotificationProvider, useNotifications } from './contexts/NotificationContext';
import KeepAwake from 'react-native-keep-awake';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaProvider, useSafeAreaInsets } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';

import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import HomeScreen from './screens/HomeScreen';
import TransactionsScreen from './screens/TransactionsScreen';
import NotificationsScreen from './screens/NotificationsScreen';
import SettingsScreen from './screens/SettingsScreen';
import LegalInfoScreen from './screens/LegalInfoScreen';
import WebviewScreen from './screens/WebviewScreen';
import OrderScreen from './screens/OrderScreen';
import PaymentScreen from './screens/PaymentScreen';
import TransactionStatsScreen from './screens/TransactionStatsScreen';
import OrderDetailScreen from './screens/OrderDetailScreen';

Tts.setDefaultLanguage('vi-VN');
Tts.setDefaultRate(0.4);
Tts.setDefaultPitch(1.0);
Tts.setDefaultVoice('vi-vn-x-gft-local');

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();
const MyLightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: '#ffffff',
    card: '#ffffff',
    text: '#2c3e50',
    border: '#ccc',
    primary: '#308a5a',
    notification: '#308a5a',
  },
};
function MyTabs() {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      initialRouteName="HomeTab"
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: '#308a5a',
        tabBarInactiveTintColor: '#308a5a',
        tabBarStyle: {
          backgroundColor: '#fff',
          height: 60 + insets.bottom,
          paddingBottom: insets.bottom,
          borderTopWidth: 0.5,
          borderTopColor: '#ccc',
        },
        tabBarIcon: ({ color }) => {
          const icons: any = {
            HomeTab: 'home',
            Transactions: 'file',
            Order: 'plus-circle',
            Notifications: 'bell',
            Settings: 'cog',
          };
          const iconName = icons[route.name];
          const size = route.name === 'Order' ? 36 : 24;
          return <FontAwesome name={iconName} size={size} color={color} />;
        },
        tabBarLabelStyle: {
          fontSize: 12,
          marginBottom: 4,
        },
      })}
    >
      <Tab.Screen name="HomeTab" component={HomeScreen} options={{ tabBarLabel: 'Trang chủ' }} />
      <Tab.Screen name="Transactions" component={TransactionsScreen} options={{ tabBarLabel: 'Giao dịch' }} />
      <Tab.Screen
        name="Order"
        component={OrderScreen}
        options={{
          tabBarLabel: '',
          tabBarIconStyle: { marginTop: -12 },
          tabBarButton: (props) => (
            <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
              <FontAwesome name="plus-circle" size={50} color="#308a5a" onPress={() => navigate('Order')} />
            </View>
          ),
        }}
      />
      <Tab.Screen name="Notifications" component={NotificationsScreen} options={{ tabBarLabel: 'Thông báo' }} />
      <Tab.Screen name="Settings" component={SettingsScreen} options={{ tabBarLabel: 'Cài đặt' }} />
    </Tab.Navigator>
  );
}

const toastConfig = {
  success: ({ text1, text2 }: any) => (
    <View style={styles.toastContainer}>
      <Image source={{ uri: 'https://docs.pay2s.vn/assets/favicon.png' }} style={styles.toastIcon} />
      <View style={styles.toastContent}>
        <Text style={styles.toastTitle}>{text1}</Text>
        <Text style={styles.toastMessage}>{text2}</Text>
      </View>
    </View>
  ),
};

const MainApp = () => {
  KeepAwake.activate();
  const [token, setToken] = useState('');
  const { ttsEnabled } = useTts();
  const { addNotification } = useNotifications();



  const requestNotificationPermission = async () => {
    if (Platform.OS === 'android' && Platform.Version >= 33) {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        {
          title: 'Cho phép gửi thông báo',
          message: 'Pay2S muốn gửi thông báo cho bạn khi có giao dịch mới.',
          buttonPositive: 'Cho phép',
          buttonNegative: 'Từ chối',
        }
      );
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        Alert.alert('⚠️ Đã từ chối nhận thông báo');
      }
    }
  };

  useEffect(() => {
    requestNotificationPermission();

    let socket: any = null;
    const initSocket = async () => {
      const t = await messaging().getToken();
      setToken(t);
      const userId = await AsyncStorage.getItem('userId');
      if (!userId) return;

      socket = io("https://socket.pay2s.vn:4024", { transports: ['websocket'] });

      socket.on('connect', () => {
        console.log('✅ Socket connected:', socket.id);
        socket.emit('register', userId);
      });

      socket.on('connect_error', (err: any) => {
        console.log('❌ Socket connection error:', err.message);
      });

      socket.on('disconnect', () => {
        console.log('⚠️ Socket disconnected');
      });

      socket.on('new_notification', (data: any) => {
        Toast.show({
          type: 'success',
          text1: data.title || 'Thông báo mới',
          text2: data.content || '',
          onPress: () => {
            Toast.hide();
            navigate('Notifications');
          },
        });

        addNotification({
          id: data.id,
          title: data.title,
          content: data.content,
          is_read: 0,
          created_at: data.created_at,
        });
      });
    };

    initSocket();

    const unsubscribeForeground = messaging().onMessage(async remoteMessage => {
      const amount = remoteMessage.data?.amount;
      const title = remoteMessage.notification?.title || 'Thông báo';
      const body = remoteMessage.notification?.body || '';

      if (ttsEnabled && amount) {
        const message = `Đã nhận ${Number(amount).toLocaleString('vi-VN')} đồng.`;
        setTimeout(() => Tts.speak(message), 300);
      }

      Toast.show({
        type: 'success',
        text1: title,
        text2: body,
      });
    });

    const unsubscribeBackground = messaging().onNotificationOpenedApp(remoteMessage => {
      if (navigationRef.isReady()) {
        navigationRef.current?.navigate('Transaction');
      }
    });

    messaging().getInitialNotification().then(remoteMessage => {
      if (remoteMessage && navigationRef.isReady()) {
        navigationRef.current?.navigate('Transaction');
      }
    });

    return () => {
      unsubscribeForeground();
      unsubscribeBackground();
      if (socket) socket.disconnect();
    };
  }, [ttsEnabled]);

  return (
    <>
      <NavigationContainer ref={navigationRef} theme={MyLightTheme}>
        <Stack.Navigator initialRouteName="Login" screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
          <Stack.Screen name="Home" component={MyTabs} />
          <Stack.Screen name="Transaction" component={TransactionsScreen} />
          <Stack.Screen name="Notifications" component={NotificationsScreen} />
          <Stack.Screen name="LegalInfo" component={LegalInfoScreen} />
          <Stack.Screen name="Webview" component={WebviewScreen} />
          <Stack.Screen name="Order" component={OrderScreen} />
          <Stack.Screen name="Payment" component={PaymentScreen} />
          <Stack.Screen name="OrderDetail" component={OrderDetailScreen} />
        </Stack.Navigator>
      </NavigationContainer>
      <Toast config={toastConfig} />
    </>
  );
};

const App = () => (
  <SafeAreaProvider>
    <TtsProvider>
      <NotificationProvider>
        <MainApp />
      </NotificationProvider>
    </TtsProvider>
  </SafeAreaProvider>
);

export default App;

const styles = StyleSheet.create({
  toastContainer: {
    flexDirection: 'row',
    backgroundColor: '#f9fdfb',
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 5,
    elevation: 5,
  },
  toastIcon: { width: 24, height: 24, marginRight: 12 },
  toastContent: { flex: 1 },
  toastTitle: { fontWeight: 'bold', fontSize: 16, color: '#2c3e50' },
  toastMessage: { fontSize: 14, color: '#555', marginTop: 2 },
});
