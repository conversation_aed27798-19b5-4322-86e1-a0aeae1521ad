import React, { useEffect, useState } from 'react';
import {
  View, Text, StyleSheet, TextInput, TouchableOpacity,
  Alert, ScrollView, Modal, ActivityIndicator, Dimensions,
  Platform, Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import PageLayout from '../components/PageLayout';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import moment from 'moment';

const { width } = Dimensions.get('window');

// Simple gradient replacement component
const SimpleGradient = ({ colors, style, children }) => (
  <View style={[style, { backgroundColor: colors[0] }]}>
    {children}
  </View>
);

export default function OrderScreen() {
  const navigation = useNavigation();
  const [amount, setAmount] = useState('');
  const [selectedBank, setSelectedBank] = useState<any>(null);
  const [bankList, setBankList] = useState([]);
  const [filteredBanks, setFilteredBanks] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBankModal, setShowBankModal] = useState(false);
  const [orderHistory, setOrderHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(true);

  const formatCurrency = (value: string) => {
    const onlyNumbers = value.replace(/[^\d]/g, '');
    return onlyNumbers.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  const numberToWords = (num: number): string => {
    if (num === 0) return 'không đồng';

    const ones = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
    const tens = ['', '', 'hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', 'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi'];
    const scales = ['', 'nghìn', 'triệu', 'tỷ'];

    const convertHundreds = (n: number): string => {
      let result = '';
      const hundred = Math.floor(n / 100);
      const remainder = n % 100;
      const ten = Math.floor(remainder / 10);
      const one = remainder % 10;

      if (hundred > 0) {
        result += ones[hundred] + ' trăm';
      }

      if (ten > 1) {
        result += (result ? ' ' : '') + tens[ten];
        if (one > 0) {
          result += ' ' + ones[one];
        }
      } else if (ten === 1) {
        result += (result ? ' ' : '') + 'mười';
        if (one > 0) {
          result += ' ' + ones[one];
        }
      } else if (one > 0) {
        result += (result ? ' ' : '') + ones[one];
      }

      return result;
    };

    const groups = [];
    let tempNum = num;

    while (tempNum > 0) {
      groups.push(tempNum % 1000);
      tempNum = Math.floor(tempNum / 1000);
    }

    let result = '';
    for (let i = groups.length - 1; i >= 0; i--) {
      if (groups[i] > 0) {
        const groupText = convertHundreds(groups[i]);
        if (groupText) {
          result += (result ? ' ' : '') + groupText;
          if (i > 0) {
            result += ' ' + scales[i];
          }
        }
      }
    }

    return result + ' đồng';
  };

  const fetchBankList = async () => {
    const token = await AsyncStorage.getItem('userToken');
    const userId = await AsyncStorage.getItem('userId');
    const data = `action=bank_account&user_id=${userId}`;

    try {
      const res = await axios.post('https://api.pay2s.vn/api/v1/bank', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`,
        },
      });
      if (res.data.status && Array.isArray(res.data.banks)) {
        setBankList(res.data.banks);
        setFilteredBanks(res.data.banks);
      }
    } catch (err) {
      Alert.alert('Lỗi', 'Không lấy được danh sách ngân hàng.');
    }
  };

  const fetchOrderHistory = async () => {
    const token = await AsyncStorage.getItem('userToken');
    setHistoryLoading(true);

    try {
      const res = await axios.post(
        'https://payment.pay2s.vn/api/v1/orders/history',
        { requestId: '' },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          }
        }
      );
      if (res.data.success && Array.isArray(res.data.data)) {
        setOrderHistory(res.data.data);
      }
    } catch (err: any) {
      console.log('❌ Lỗi fetch history:', err.response?.data || err.message);
    } finally {
      setHistoryLoading(false);
    }
  };

  useEffect(() => {
    fetchBankList();
    fetchOrderHistory();
  }, []);

  const translateStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return { label: 'Thành công', color: '#308a5a', icon: 'checkmark-circle' };
      case 'pending':
        return { label: 'Chờ thanh toán', color: '#f39c12', icon: 'time' };
      case 'canceled':
      case 'cancelled':
        return { label: 'Đã huỷ', color: '#e74c3c', icon: 'close-circle' };
      case 'failed':
        return { label: 'Thất bại', color: '#e74c3c', icon: 'alert-circle' };
      default:
        return { label: status, color: '#7f8c8d', icon: 'help-circle' };
    }
  };

  const createOrder = async () => {
    if (!amount || !selectedBank) {
      Alert.alert('Thiếu thông tin', 'Vui lòng nhập số tiền và chọn ngân hàng.');
      return;
    }

    setLoading(true);
    const partnerCode = await AsyncStorage.getItem('partnerCode');
    const token = await AsyncStorage.getItem('userToken');
    const requestId = 'ORDER' + Date.now();
    const amountNumber = amount.replace(/\./g, '');
    const body = {
      partnerCode,
      amount: parseFloat(amountNumber),
      orderInfo: 'Thanh toán đơn hàng',
      requestId,
      bankIds: selectedBank.id
    };

    try {
      const res = await axios.post(
        'https://payment.pay2s.vn/api/v1/orders/create',
        body,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (res.data.resultCode === 0) {
        navigation.navigate('Payment', {
          name: res.data.name,
          orderId: res.data.requestId,
          amount: res.data.amount,
          qrcode: res.data.qrcode,
          payUrl: res.data.payUrl,
          bank_list: res.data.bank_list
        });
      } else {
        Alert.alert('Thất bại', res.data.message || 'Không tạo đơn được.');
      }
    } catch (err: any) {
      console.log('❌ Lỗi gửi:', err.response?.data || err.message);
      Alert.alert('Lỗi', 'Không gửi được yêu cầu. Kiểm tra kết nối hoặc định dạng.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
    const filtered = bankList.filter((bank: any) =>
      bank.bankName.toLowerCase().includes(text.toLowerCase()) ||
      bank.accountNumber?.includes(text) ||
      bank.vaNumber?.includes(text)
    );
    setFilteredBanks(filtered);
  };

  return (
    <PageLayout>
      <ScrollView 
        style={styles.container}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <SimpleGradient
          colors={['#308a5a', '#4caf50']}
          style={styles.headerCard}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name="receipt-outline" size={32} color="#fff" />
            </View>
            <Text style={styles.headerTitle}>Tạo Hóa Đơn</Text>
            <Text style={styles.headerSubtitle}>Thanh toán nhanh chóng và an toàn</Text>
          </View>
        </SimpleGradient>

        {/* Create Order Form */}
        <View style={styles.formCard}>
          <Text style={styles.formTitle}>Thông tin thanh toán</Text>
          
          {/* Amount Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>
              <Icon name="cash-outline" size={16} color="#308a5a" />
              {' '}Số tiền
            </Text>
            <View style={styles.inputWrapper}>
              <Text style={styles.currencySymbol}>₫</Text>
              <TextInput
                style={styles.amountInput}
                keyboardType="numeric"
                placeholder="0"
                placeholderTextColor="#a0a0a0"
                value={amount}
                onChangeText={(text) => setAmount(formatCurrency(text))}
              />
            </View>
            {amount ? (
              <Text style={styles.amountPreview}>
                {numberToWords(parseInt(amount.replace(/\./g, ''), 10))}
              </Text>
            ) : null}
          </View>

          {/* Bank Selector */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>
              <Icon name="card-outline" size={16} color="#308a5a" />
              {' '}Ngân hàng nhận
            </Text>
            <TouchableOpacity 
              style={styles.bankSelectorButton} 
              onPress={() => setShowBankModal(true)}
              activeOpacity={0.7}
            >
              <View style={styles.bankSelectorContent}>
                {selectedBank ? (
                  <View style={styles.selectedBankInfo}>
                    <Text style={styles.selectedBankName}>{selectedBank.bankName}</Text>
                    <Text style={styles.selectedBankAccount}>
                      STK: {selectedBank.vaNumber || selectedBank.accountNumber}
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.bankPlaceholder}>Chọn ngân hàng</Text>
                )}
                <Icon name="chevron-forward" size={20} color="#308a5a" />
              </View>
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity 
            style={[styles.submitButton, loading && styles.submitButtonDisabled]} 
            onPress={createOrder}
            disabled={loading}
            activeOpacity={0.8}
          >
            <View style={styles.submitButtonContent}>
              {loading ? (
                <>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={styles.submitButtonText}>Đang tạo...</Text>
                </>
              ) : (
                <>
                  <Icon name="add-circle-outline" size={20} color="#fff" />
                  <Text style={styles.submitButtonText}>Tạo Hóa Đơn</Text>
                </>
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Order History Section */}
        <View style={styles.historyCard}>
          <Text style={styles.historyTitle}>
            <Icon name="time-outline" size={18} color="#308a5a" />
            {' '}Lịch sử đơn hàng
          </Text>

          {historyLoading ? (
            <View style={styles.historyLoading}>
              <ActivityIndicator size="large" color="#308a5a" />
              <Text style={styles.historyLoadingText}>Đang tải lịch sử...</Text>
            </View>
          ) : orderHistory.length === 0 ? (
            <View style={styles.emptyHistory}>
              <Icon name="document-outline" size={48} color="#bdc3c7" />
              <Text style={styles.emptyHistoryText}>Chưa có đơn hàng nào</Text>
            </View>
          ) : (
            <View style={styles.orderList}>
              {orderHistory.map((order: any) => {
                const statusInfo = translateStatus(order.status);
                return (
                  <TouchableOpacity
                    key={order.id}
                    style={styles.orderCard}
                    onPress={() => navigation.navigate('OrderDetail', { orderId: order.order_id })}
                    activeOpacity={0.7}
                  >
                    <View style={styles.orderHeader}>
                      <Text
                        style={styles.orderIdText}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        #{order.order_id}
                      </Text>
                      <View style={[styles.statusBadge, { backgroundColor: `${statusInfo.color}20` }]}>
                        <Icon name={statusInfo.icon} size={14} color={statusInfo.color} />
                        <Text
                          style={[styles.statusText, { color: statusInfo.color }]}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {statusInfo.label}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.orderBody}>
                      <View style={styles.orderRow}>
                        <Icon name="cash-outline" size={16} color="#7f8c8d" />
                        <Text style={styles.orderLabel}>Số tiền:</Text>
                        <Text
                          style={styles.orderAmount}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {Number(order.amount).toLocaleString()} ₫
                        </Text>
                      </View>

                      <View style={styles.orderRow}>
                        <Icon name="calendar-outline" size={16} color="#7f8c8d" />
                        <Text style={styles.orderLabel}>Ngày tạo:</Text>
                        <Text
                          style={styles.orderValue}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {moment(order.created_at).format('DD/MM/YYYY HH:mm')}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.orderFooter}>
                      <Icon name="chevron-forward" size={16} color="#308a5a" />
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bank Selection Modal */}
      <Modal visible={showBankModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Chọn ngân hàng</Text>
              <TouchableOpacity
                onPress={() => {
                  setShowBankModal(false);
                  setSearchTerm('');
                  setFilteredBanks(bankList);
                }}
                style={styles.modalCloseButton}
              >
                <Icon name="close" size={24} color="#7f8c8d" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Icon name="search-outline" size={20} color="#7f8c8d" style={styles.searchIcon} />
              <TextInput
                placeholder="Tìm theo tên hoặc STK"
                style={styles.searchInput}
                value={searchTerm}
                onChangeText={handleSearch}
                placeholderTextColor="#a0a0a0"
              />
            </View>

            <ScrollView style={styles.bankListContainer}>
              {filteredBanks.map(bank => (
                <TouchableOpacity
                  key={bank.id}
                  style={styles.bankOption}
                  onPress={() => {
                    setSelectedBank(bank);
                    setShowBankModal(false);
                    setSearchTerm('');
                    setFilteredBanks(bankList);
                  }}
                  activeOpacity={0.7}
                >
                  <View style={styles.bankOptionContent}>
                    <View style={styles.bankIconContainer}>
                      <Icon name="card-outline" size={24} color="#308a5a" />
                    </View>
                    <View style={styles.bankInfo}>
                      <Text style={styles.bankName}>{bank.bankName}</Text>
                      <Text style={styles.bankAccount}>STK: {bank.vaNumber || bank.accountNumber}</Text>
                    </View>
                    <Icon name="chevron-forward" size={16} color="#bdc3c7" />
                  </View>
                </TouchableOpacity>
              ))}
              {filteredBanks.length === 0 && (
                <View style={styles.emptyBankList}>
                  <Icon name="search-outline" size={48} color="#bdc3c7" />
                  <Text style={styles.emptyBankText}>Không tìm thấy ngân hàng phù hợp</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingBottom: 30,
  },

  // Header Card
  headerCard: {
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    padding: 24,
    alignItems: 'center',
  },
  headerIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },

  // Form Card
  formCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
    textAlign: 'center',
  },

  // Input Styles
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#e9ecef',
    paddingHorizontal: 20,
    paddingVertical: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#308a5a',
    marginRight: 10,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#2c3e50',
  },
  amountPreview: {
    marginTop: 10,
    fontSize: 14,
    color: '#308a5a',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Bank Selector
  bankSelectorButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#e9ecef',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bankSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedBankInfo: {
    flex: 1,
  },
  selectedBankName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  selectedBankAccount: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  bankPlaceholder: {
    fontSize: 16,
    color: '#a0a0a0',
    flex: 1,
  },

  // Submit Button
  submitButton: {
    backgroundColor: '#308a5a',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    marginTop: 10,
  },
  submitButtonDisabled: {
    backgroundColor: '#a0a0a0',
    shadowOpacity: 0.1,
  },
  submitButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },

  // History Card
  historyCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 16,
    textAlign: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyLoading: {
    padding: 40,
    alignItems: 'center',
  },
  historyLoadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#7f8c8d',
  },
  emptyHistory: {
    padding: 40,
    alignItems: 'center',
  },
  emptyHistoryText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 12,
  },

  // Order List
  orderList: {
    gap: 12,
  },
  orderCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#308a5a',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderIdText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    maxWidth: 140,
    flexShrink: 1,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    flexShrink: 1,
  },
  orderBody: {
    marginBottom: 8,
  },
  orderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderLabel: {
    fontSize: 14,
    color: '#7f8c8d',
    marginLeft: 8,
    marginRight: 8,
    minWidth: 80,
    flexShrink: 0,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#308a5a',
    flexShrink: 1,
    textAlign: 'right',
  },
  orderValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '500',
    flexShrink: 1,
    textAlign: 'right',
  },
  orderFooter: {
    alignItems: 'flex-end',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  modalCloseButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e9ecef',
    paddingHorizontal: 16,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#2c3e50',
  },
  bankListContainer: {
    maxHeight: 400,
  },
  bankOption: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  bankOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bankIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8fff9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  bankInfo: {
    flex: 1,
  },
  bankName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  bankAccount: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  emptyBankList: {
    padding: 40,
    alignItems: 'center',
  },
  emptyBankText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 12,
  },
});
