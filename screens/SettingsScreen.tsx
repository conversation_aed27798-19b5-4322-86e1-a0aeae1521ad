// ✅ SETTINGS SCREEN CHUẨN UI NHƯ HÌNH ĐÍNH KÈM
import React, { useState, useEffect } from 'react';
import {
  View, Text, StyleSheet, ScrollView,
  Switch, TouchableOpacity, Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { useTts } from '../contexts/TtsContext';
import DeviceInfo from 'react-native-device-info';
import { useNotifications } from '../contexts/NotificationContext';
import PageLayout from '../components/PageLayout';
import axios from 'axios';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Feather from 'react-native-vector-icons/Feather';

export default function SettingsScreen() {
  const { ttsEnabled, setTtsEnabled } = useTts();
  const { notifications } = useNotifications();
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const navigation = useNavigation();

  const fetchProfile = async () => {
    const token = await AsyncStorage.getItem('userToken');
    const userId = await AsyncStorage.getItem('userId');
    if (!token || !userId) return;
    const data = `action=get_profile&user_id=${userId}`;

    try {
      const res = await axios.post('https://api.pay2s.vn/api/v1/user', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`,
        }
      });
      if (res.data.status) setProfile(res.data.message);
    } catch (err) {}
  };

  useEffect(() => {
    fetchProfile();
    AsyncStorage.getItem('biometric_enabled').then(v => setBiometricEnabled(v === 'true'));
  }, []);

  const toggleTTS = async () => {
    const newVal = !ttsEnabled;
    setTtsEnabled(newVal);
    await AsyncStorage.setItem('tts_enabled', newVal.toString());
  };



  const toggleBiometric = async () => {
    const newVal = !biometricEnabled;
    setBiometricEnabled(newVal);
    await AsyncStorage.setItem('biometric_enabled', newVal.toString());
  };

  const logout = async () => {
    await AsyncStorage.clear();
    Alert.alert('Đăng xuất thành công');
    navigation.reset({ index: 0, routes: [{ name: 'Login' }] });
  };

  return (
    <PageLayout>
      <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
        {/* Profile Card */}
        {profile && (
          <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.avatarContainer}>
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>
                    {profile.firstname.charAt(0)}{profile.lastname.charAt(0)}
                  </Text>
                </View>
                <View style={styles.statusBadge}>
                  <MaterialCommunityIcons name="check" size={12} color="#fff" />
                </View>
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{profile.firstname} {profile.lastname}</Text>
                <Text style={styles.profileUsername}>@{profile.username}</Text>
              </View>
            </View>

            <View style={styles.profileDetails}>
              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <MaterialCommunityIcons name="email-outline" size={18} color="#308a5a" />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Email</Text>
                  <Text style={styles.detailValue}>{profile.email}</Text>
                </View>
              </View>

              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <MaterialCommunityIcons name="package-variant" size={18} color="#308a5a" />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Gói dịch vụ</Text>
                  <Text style={styles.detailValue}>
                    {profile.current_plan || 'Cá nhân'} ({profile.billingcycle})
                  </Text>
                </View>
              </View>

              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <MaterialCommunityIcons name="calendar-clock" size={18} color="#308a5a" />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Hết hạn</Text>
                  <Text style={styles.detailValue}>
                    {profile.expire_date?.split('-').reverse().join('/')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Settings Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Cài đặt ứng dụng</Text>

          <View style={styles.settingsCard}>
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIconContainer, { backgroundColor: '#e8f5e9' }]}>
                  <MaterialCommunityIcons name="volume-high" size={20} color="#308a5a" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>Đọc giao dịch bằng giọng nói</Text>
                  <Text style={styles.settingDesc}>Tự động đọc số tiền khi có giao dịch mới</Text>
                </View>
              </View>
              <Switch
                value={ttsEnabled}
                onValueChange={toggleTTS}
                trackColor={{ true: '#308a5a', false: '#e0e0e0' }}
                thumbColor={ttsEnabled ? '#fff' : '#f4f3f4'}
              />
            </View>

            <View style={styles.divider} />

            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIconContainer, { backgroundColor: '#fff3e0' }]}>
                  <MaterialCommunityIcons name="fingerprint" size={20} color="#ff9800" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>Đăng nhập bằng vân tay</Text>
                  <Text style={styles.settingDesc}>Cho phép xác thực vân tay khi mở ứng dụng</Text>
                </View>
              </View>
              <Switch
                value={biometricEnabled}
                onValueChange={toggleBiometric}
                trackColor={{ true: '#ff9800', false: '#e0e0e0' }}
                thumbColor={biometricEnabled ? '#fff' : '#f4f3f4'}
              />
            </View>
          </View>
        </View>

        {/* Other Options */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Khác</Text>

          <TouchableOpacity style={styles.optionItem} onPress={() => navigation.navigate('LegalInfo')}>
            <View style={styles.optionLeft}>
              <View style={[styles.settingIconContainer, { backgroundColor: '#e3f2fd' }]}>
                <MaterialCommunityIcons name="file-document-outline" size={20} color="#2196f3" />
              </View>
              <Text style={styles.optionText}>Thông tin pháp lý</Text>
            </View>
            <MaterialCommunityIcons name="chevron-right" size={24} color="#bbb" />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={logout}>
          <MaterialCommunityIcons name="logout" size={20} color="#fff" style={{ marginRight: 8 }} />
          <Text style={styles.logoutText}>Đăng xuất</Text>
        </TouchableOpacity>

        {/* Version Info */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>
            Phiên bản {DeviceInfo.getVersion()} (build {DeviceInfo.getBuildNumber()})
          </Text>
        </View>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f8f9fa'
  },

  // Profile Card Styles
  profileCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 12,
    elevation: 6
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#308a5a',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#308a5a',
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 4
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold'
  },
  statusBadge: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4caf50',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff'
  },
  profileInfo: {
    flex: 1
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4
  },
  profileUsername: {
    fontSize: 14,
    color: '#7f8c8d',
    fontWeight: '500'
  },
  profileDetails: {
    gap: 16
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  detailContent: {
    flex: 1
  },
  detailLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 2
  },
  detailValue: {
    fontSize: 15,
    color: '#2c3e50',
    fontWeight: '500'
  },

  // Section Styles
  sectionContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 16,
    paddingHorizontal: 4
  },

  // Settings Card Styles
  settingsCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 3
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  settingIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  settingContent: {
    flex: 1
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4
  },
  settingDesc: {
    fontSize: 13,
    color: '#7f8c8d',
    lineHeight: 18
  },
  divider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 20
  },

  // Option Item Styles
  optionItem: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 3
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2c3e50'
  },

  // Logout Button Styles
  logoutButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#e74c3c',
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 4
  },
  logoutText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16
  },

  // Version Styles
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 16
  },
  versionText: {
    fontSize: 12,
    color: '#bdc3c7',
    textAlign: 'center'
  }
});
