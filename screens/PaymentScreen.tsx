import React, { useEffect, useState, useRef  } from 'react';
import {
  View, Text, StyleSheet, Image, TouchableOpacity, Alert, ActivityIndicator, ScrollView, Share
} from 'react-native';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Vibration } from 'react-native';
import Tts from 'react-native-tts';
import { useTts } from '../contexts/TtsContext';
Tts.setDefaultLanguage('vi-VN');
Tts.setDefaultRate(0.4);
Tts.setDefaultPitch(1.0);
Tts.setDefaultVoice('vi-vn-x-gft-local');
export default function PaymentScreen({ route }: any) {
  const { orderId, amount, qrcode, bank_list, payUrl, name  } = route.params || {};
  const navigation = useNavigation();
  const { ttsEnabled } = useTts();
  const bank = Array.isArray(bank_list) && bank_list.length > 0 ? bank_list[0] : null;

  const [timeLeft, setTimeLeft] = useState(600); // 10 phút
  const [status, setStatus] = useState('pending');
  const firstCheckDone = useRef(false); // ✅ đặt ngoài useEffect
  const hasSpoken = useRef(false); // 👈 thêm dòng này
  const formatTime = (seconds: number) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m}:${s < 10 ? '0' : ''}${s}`;
  };

  // Tự động kiểm tra trạng thái mỗi 5 giây
useEffect(() => {


  const fetchStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const res = await axios.post('https://payment.pay2s.vn/api/v1/orders/status', {
        requestId: orderId
      }, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        }
      });

      if (res.data.resultCode === 0) {
        setStatus(res.data.status);

        if (!firstCheckDone.current) {
          firstCheckDone.current = true;
          return;
        }

        if (res.data.status === 'completed') {
          if (!hasSpoken.current) {
            hasSpoken.current = true;
            if (ttsEnabled) {
              Tts.stop();
              const amountFormatted = parseInt(amount, 10).toLocaleString('vi-VN');
              Tts.speak(`Nhận ${amountFormatted} đồng.`);
            }
            // Vibration.vibrate(300);
          }

          clearInterval(interval); // ✅ Dừng vòng lặp kiểm tra

          setStatus('completed');
          setTimeout(() => navigation.reset({ index: 0, routes: [{ name: 'Home' }] }), 5000);
        } else if (res.data.status === 'canceled') {
          Alert.alert('❌ Đơn hàng đã bị huỷ');
          navigation.goBack();
        }
      }
    } catch (err) {
      console.log('Lỗi kiểm tra trạng thái:', err);
    }
  };

  const interval = setInterval(fetchStatus, 2000);
  fetchStatus();

  return () => clearInterval(interval);
}, [amount, navigation, orderId, ttsEnabled]);
  // Đếm ngược hết hạn đơn
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          Alert.alert('Hết hạn', 'Đơn hàng đã hết hạn.');
          navigation.goBack();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigation]);

  const handleShareLink = async () => {
    try {
      if (!payUrl) {
        Alert.alert('Lỗi', 'Không có link thanh toán.');
        return;
      }
      await Share.share({
        message: `Thanh toán đơn hàng ${name}: ${payUrl}`,
      });
    } catch (error) {
      Alert.alert('Lỗi', 'Không chia sẻ được link.');
    }
  };

  const handleCancelOrder = async () => {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const res = await axios.post('https://payment.pay2s.vn/api/v1/orders/cancel', {
        requestId: orderId
      }, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        }
      });

      if (res.data.resultCode === 0) {
        Alert.alert('Đã huỷ đơn hàng');
        navigation.goBack();
      } else {
        Alert.alert('Lỗi', res.data.message || 'Không huỷ được đơn');
      }
    } catch (err) {
      Alert.alert('Lỗi', 'Không thể gửi yêu cầu huỷ');
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Thanh toán đơn hàng</Text>
      <Text style={styles.amount}>{parseInt(amount, 10).toLocaleString('vi-VN')}đ</Text>

      {qrcode ? (
        <Image source={{ uri: qrcode }} style={styles.qrImage} resizeMode="contain" />
      ) : (
        <ActivityIndicator size="large" color="#308a5a" />
      )}

      <Text style={styles.orderId}>Mã đơn: {orderId}</Text>

      {bank ? (
        <>
          <View style={styles.bankCard}>
            <Text style={styles.bankInfo}><Text style={styles.bankLabel}>Ngân hàng:</Text> {bank.bank_name}</Text>
            <Text style={styles.bankInfo}><Text style={styles.bankLabel}>Số tài khoản:</Text> {bank.account_number}</Text>
            <Text style={styles.bankInfo}><Text style={styles.bankLabel}>Chủ tài khoản:</Text> {bank.name}</Text>
          </View>
        </>
      ) : (
        <Text style={[styles.bankInfo, { color: '#999' }]}>Không tìm thấy thông tin ngân hàng</Text>
      )}

      <Text style={[
        styles.status,
        status === 'completed' ? styles.success : styles.pending
      ]}>
        {status === 'pending' ? '⏳ Đang chờ thanh toán' : '✅ Thanh toán thành công'}
      </Text>

      {status !== 'completed' && (
        <Text style={styles.timer}>⏱ Còn lại: {formatTime(timeLeft)}</Text>
      )}

      {status === 'pending' && (
           <>
        <View style={styles.actionRow}>
          <TouchableOpacity style={styles.cancelBtn} onPress={handleCancelOrder}>
            <Text style={styles.cancelText}>Huỷ đơn</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.backBtn} onPress={() => navigation.goBack()}>
            <Text style={styles.backText}>Quay lại</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={handleShareLink} style={styles.shareBtn}>
              <Text style={styles.shareText}>📤 Chia sẻ hóa đơn thanh toán</Text>
            </TouchableOpacity>
       </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
    shareBtn: {
      marginTop: 10,
      backgroundColor: '#308a5a',
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
    },
    shareText: {
      color: '#fff',
      fontWeight: 'bold',
      fontSize: 15
    },
    actionRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
      marginTop: 30
    },
    cancelBtn: {
      flex: 1,
      backgroundColor: '#e74c3c',
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center'
    },
    cancelText: {
      color: '#fff',
      fontWeight: 'bold'
    },
    backBtn: {
      flex: 1,
      backgroundColor: '#ccc',
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center'
    },
    backText: {
      fontWeight: 'bold',
      color: '#2c3e50'
    },
  container: {
    alignItems: 'center', justifyContent: 'center',
    padding: 20, backgroundColor: '#fff', flexGrow: 1
  },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 12, color: '#308a5a' },
  amount: { fontSize: 30, color: '#e74c3c', fontWeight: 'bold', marginBottom: 20 },
  qrImage: {
    width: 240, height: 240, borderRadius: 16,
    borderWidth: 1, borderColor: '#ddd', marginBottom: 20,
    backgroundColor: '#fff',
    shadowColor: '#000', shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2, shadowRadius: 4,
    elevation: 5,
  },
  bankCard: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#eee',
    marginTop: 12,
    width: '100%'
  },
  bankLabel: {
    fontWeight: 'bold',
    color: '#333'
  },
  orderId: { fontSize: 15, color: '#888', marginBottom: 12 },
  bankInfo: { fontSize: 16, color: '#444', marginTop: 4 },
  bankHighlight: { fontWeight: 'bold', color: '#2c3e50' },
  timer: { fontSize: 18, color: '#e67e22', marginTop: 12, fontWeight: 'bold' },
 status: {
   fontSize: 16,
   fontWeight: 'bold',
   marginTop: 12,
   paddingVertical: 6,
   paddingHorizontal: 14,
   borderRadius: 20,
   overflow: 'hidden'
 },
 success: { backgroundColor: '#d4edda', color: '#155724' },
 pending: { backgroundColor: '#fff3cd', color: '#856404' },
  cancelBtn: {
    flex: 1,
    backgroundColor: '#ff4d4f',
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center'
  },
  cancelText: { color: '#fff', fontWeight: 'bold' },
backBtn: {
  flex: 1,
  backgroundColor: '#dcdcdc',
  paddingVertical: 14,
  borderRadius: 10,
  alignItems: 'center'
},
  backText: { fontWeight: 'bold', color: '#2c3e50' }
});
