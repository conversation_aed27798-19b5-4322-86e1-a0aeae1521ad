import { useState, useCallback } from 'react';
import axios from 'axios';

export interface BlogPost {
  id: number;
  title: string;
  link: string;
  image: string | null;
  excerpt: string;
}

export const useBlogPosts = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);

  const fetchBlogPosts = useCallback(async () => {
    try {
      const res = await axios.get(
        'https://pay2s.vn/blog/wp-json/wp/v2/posts?per_page=10&_embed'
      );
      const data = res.data.map((item: any) => {
        const thumbnail =
          item._embedded?.['wp:featuredmedia']?.[0]?.source_url || null;
        return {
          id: item.id,
          title: item.title.rendered,
          link: item.link,
          image: thumbnail,
          excerpt: item.excerpt.rendered.replace(/<[^>]*>?/gm, ''),
        };
      });
      setBlogPosts(data);
    } catch (err) {
      console.error('Lỗi lấy blog:', err);
    }
  }, []);

  return {
    blogPosts,
    fetchBlogPosts,
  };
};
