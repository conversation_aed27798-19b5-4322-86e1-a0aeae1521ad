import { useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import moment from 'moment';
import { Alert } from 'react-native';

interface ProfileResponse {
  status: boolean;
  message: {
    firstname: string;
    lastname: string;
    username: string;
    credit: number;
    company_name?: string;
    current_plan?: string;
  };
}

interface InvoiceResponse {
  status: boolean;
  invoices: {
    status: string;
    created_at: string;
  }[];
  message?: string;
}

export interface UserData {
  name: string;
  username: string;
  companyName: string;
  currentPlan: string;
  balance: number;
  pendingTransactions: number;
  transactionsToday: number;
  transactionsYesterday: number;
  transactionsThisMonth: number;
}

export const useUserData = () => {
  const [userData, setUserData] = useState<UserData>({
    name: '',
    username: '',
    companyName: '',
    currentPlan: '',
    balance: 0,
    pendingTransactions: 0,
    transactionsToday: 0,
    transactionsYesterday: 0,
    transactionsThisMonth: 0,
  });
  const [loading, setLoading] = useState(true);

  const fetchUserData = useCallback(async () => {
    try {
      const [userToken, userId] = await Promise.all([
        AsyncStorage.getItem('userToken'),
        AsyncStorage.getItem('userId'),
      ]);

      if (!userToken || !userId) throw new Error('Thiếu token hoặc userId');

      const [profileRes, invoiceRes] = await Promise.all([
        axios.post<ProfileResponse>(
          'https://api.pay2s.vn/api/v1/user',
          `action=get_profile&user_id=${userId}`,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              Authorization: `Bearer ${userToken}`,
            },
          }
        ),
        axios.post<InvoiceResponse>(
          'https://api.pay2s.vn/api/v1/invoices',
          `action=list_bank_invoices&user_id=${userId}`,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              Authorization: `Bearer ${userToken}`,
            },
          }
        ),
      ]);

      if (!profileRes.data.status) throw new Error('Lỗi lấy thông tin tài khoản');
      const user = profileRes.data.message;

      const newUserData: UserData = {
        name: `${user.lastname} ${user.firstname}`,
        username: user.username,
        companyName: user.company_name || '',
        currentPlan: user.current_plan || '',
        balance: user.credit,
        pendingTransactions: 0,
        transactionsToday: 0,
        transactionsYesterday: 0,
        transactionsThisMonth: 0,
      };

      if (invoiceRes.data.status) {
        const invoices = invoiceRes.data.invoices;
        newUserData.pendingTransactions = invoices.filter(i => i.status === 'pending').length;
        newUserData.transactionsToday = invoices.filter(i => 
          moment(i.created_at).isSame(moment(), 'day')
        ).length;
        newUserData.transactionsYesterday = invoices.filter(i => 
          moment(i.created_at).isSame(moment().subtract(1, 'days'), 'day')
        ).length;
        newUserData.transactionsThisMonth = invoices.filter(i => 
          moment(i.created_at).isSame(moment(), 'month')
        ).length;
      }

      setUserData(newUserData);
    } catch (err: any) {
      console.error('Fetch user data error:', err);
      Alert.alert('Lỗi', err?.message || 'Không thể tải dữ liệu');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    userData,
    loading,
    fetchUserData,
  };
};
