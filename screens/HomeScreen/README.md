# HomeScreen Refactored Structure

## Overview
HomeScreen đã được refactor từ 1 file lớn (870 dòng) thành cấu trúc modular với các component và hooks riêng biệt.

## File Structure
```
screens/HomeScreen/
├── index.tsx                    # Main HomeScreen component (90 dòng)
├── HomeScreen.styles.ts         # Tất cả styles (200 dòng)
├── README.md                    # Documentation này
├── components/                  # UI Components
│   ├── UserCard.tsx            # User info với greeting animation
│   ├── StatisticsCards.tsx     # Transaction statistics cards
│   ├── BankAccountsList.tsx    # Bank accounts với toggle functionality
│   └── BlogPostsList.tsx       # Blog posts horizontal scroll
└── hooks/                      # Custom hooks cho logic
    ├── useUserData.ts          # User profile & transaction data
    ├── useBankAccounts.ts      # Bank accounts management
    └── useBlogPosts.ts         # Blog posts từ WordPress API
```

## Benefits của refactor:

### 1. **Maintainability**
- Mỗi component có responsibility riêng biệt
- Dễ debug và fix bugs
- Code dễ đọc và hiểu

### 2. **Reusability**
- Components có thể được reuse ở screens khác
- Hooks có thể được share across app
- Styles được centralized

### 3. **Performance**
- Components có thể được optimize riêng biệt
- Lazy loading có thể được implement dễ dàng
- Memory usage tốt hơn

### 4. **Testing**
- Mỗi component/hook có thể được test riêng
- Unit tests dễ viết hơn
- Integration tests rõ ràng hơn

## Components Details:

### UserCard
- Hiển thị greeting với typing animation
- User info (name, company, plan)
- Online status với blinking indicator

### StatisticsCards
- Horizontal scroll của transaction stats
- Pending, today, yesterday, this month
- Icons và colors khác nhau

### BankAccountsList
- Horizontal scroll của bank accounts
- Toggle on/off functionality
- Bank logos và detailed info

### BlogPostsList
- WordPress blog posts
- Horizontal scroll với images
- Navigation to WebView

## Hooks Details:

### useUserData
- Fetch user profile
- Calculate transaction statistics
- Error handling và loading states

### useBankAccounts
- Fetch linked bank accounts
- Toggle bank status functionality
- Loading states cho individual banks

### useBlogPosts
- Fetch từ WordPress REST API
- Parse và format blog data
- Error handling

## Usage:
```tsx
import HomeScreen from './screens/HomeScreen';
// hoặc
import HomeScreen from './screens/HomeScreen.tsx';
```

File `screens/HomeScreen.tsx` chỉ là re-export từ `./HomeScreen/index.tsx`
