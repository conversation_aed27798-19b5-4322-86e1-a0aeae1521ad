import { StyleSheet, Dimensions } from 'react-native';

const screenWidth = Dimensions.get('window').width;

export const styles = StyleSheet.create({
  // Main container styles
  scrollView: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 20,
  },

  // User Card styles
  userCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
    borderLeftWidth: 6,
    borderLeftColor: '#308a5a',
  },
  userCardTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcome: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  fullname: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#308a5a',
  },
  online: {
    fontSize: 13,
    color: '#2ecc71',
    fontWeight: '600',
    marginLeft: 4,
  },
  userAvatar: {
    width: 38,
    height: 38,
    resizeMode: 'contain',
  },
  companyBadge: {
    backgroundColor: '#ffd700',
    alignSelf: 'flex-start',
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 999,
    marginTop: 8,
    marginBottom: 4,
  },
  companyText: {
    fontSize: 12,
    color: '#000',
    fontWeight: 'bold',
  },
  planText: {
    fontSize: 13,
    color: '#666',
  },
  planName: {
    color: '#308a5a',
    fontWeight: 'bold',
  },

  // Statistics Cards styles
  statCard: {
    width: 150,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 15,
    marginRight: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 10,
  },
  statLabel: {
    fontSize: 13,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 5,
  },

  // Section styles
  section: {
    marginVertical: 20,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#ffffff',
  },
  sectionDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
    justifyContent: 'center',
  },
  line: {
    flex: 1,
    height: 1,
    backgroundColor: '#ddd',
    marginHorizontal: 5,
  },
  dividerText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#308a5a',
    paddingHorizontal: 4,
  },

  // Bank Card styles
  bankCard: {
    width: screenWidth - 80,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: '#fff',
    padding: 16,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 3,
    elevation: 1,
  },
  bankCardTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  bankCardLogo: {
    width: 60,
    height: 40,
    resizeMode: 'contain',
  },
  bankCardName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  bankCardStatus: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    flex: 1,
  },
  detailIcon: {
    width: 18,
    flexShrink: 0,
  },
  detailLabel: {
    fontSize: 13,
    color: '#888',
    marginLeft: 4,
    minWidth: 80,
    flexShrink: 0,
  },
  detailValue: {
    fontSize: 13,
    color: '#2c3e50',
    fontWeight: '500',
    flex: 1,
    flexWrap: 'wrap',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Blog Card styles
  blogCard: {
    width: 260,
    marginRight: 12,
    backgroundColor: '#fff',
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  blogTitle: {
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
    color: '#2c3e50',
  },
});
