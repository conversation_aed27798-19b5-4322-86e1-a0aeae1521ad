// ✅ TransactionStatsScreen.tsx - FINAL FIXED VERSION
// Đã sửa lỗi RenderError do chuỗi text không bọc trong <Text>

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View, Text, StyleSheet, ScrollView, RefreshControl,
  TouchableOpacity, Image, Pressable, Modal, TouchableWithoutFeedback, TextInput,
  Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import moment from 'moment';
import PageLayout from '../components/PageLayout';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useFocusEffect } from '@react-navigation/native';

const TABS = [
  { type: 'ALL', label: '🔄 Tất cả' },
  { type: 'IN', label: '🟢 Tiền vào' },
  { type: 'OUT', label: '🔴 Tiền ra' },
];

const BANKS = ['ACB', 'BIDV', 'MBBank', 'Vietcombank', 'Seabank', 'TPBank', 'OCB'];

const getBankLogo = (shortBankName) => {
  switch (shortBankName.toUpperCase()) {
    case 'MBB': return 'https://my.pay2s.vn/assets/media/banks/mbb.jpg';
    case 'BIDV': return 'https://my.pay2s.vn/assets/media/banks/bidv.jpg';
    case 'ACB': return 'https://my.pay2s.vn/assets/media/banks/acb.jpg';
    case 'MOMO': return 'https://my.pay2s.vn/assets/media/banks/momo.jpg';
    case 'VTB': return 'https://my.pay2s.vn/assets/media/banks/vtb.jpg';
    case 'TPB': return 'https://img.mservice.com.vn/momo_app_v2/img/TPB.png';
    case 'TCB': return 'https://img.mservice.com.vn/momo_app_v2/img/TCB.png';
    case 'VCB': return 'https://my.pay2s.vn/assets/media/banks/vcb.jpg';
    case 'SEAB': return 'https://my.pay2s.vn/assets/media/banks/seab.jpg';
    case 'OCB': return 'https://img.mservice.com.vn/momo_app_v2/img/OCB.png';
    default: return 'https://my.pay2s.vn/assets/media/banks/default.jpg';
  }
};

export default function TransactionStatsScreen() {
  const [transactions, setTransactions] = useState([]);
  const [grouped, setGrouped] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [tabType, setTabType] = useState('ALL');
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalIn, setTotalIn] = useState(0);
  const [totalOut, setTotalOut] = useState(0);
  const [showFilter, setShowFilter] = useState(false);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [selectedBanks, setSelectedBanks] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  // Auto-hide header states
  const scrollY = useRef(new Animated.Value(0)).current;
  const lastScrollY = useRef(0);
  const headerHeight = useRef(new Animated.Value(1)).current; // 1 = visible, 0 = hidden

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        const scrollDiff = currentScrollY - lastScrollY.current;

        // Only trigger animation if scroll difference is significant
        if (Math.abs(scrollDiff) > 3) {
          if (scrollDiff > 0 && currentScrollY > 20) {
            // Scrolling down - hide header quickly
            Animated.timing(headerHeight, {
              toValue: 0,
              duration: 100,
              useNativeDriver: false,
            }).start();
          } else if (scrollDiff < 0 || currentScrollY < 20) {
            // Scrolling up - show header quickly
            Animated.timing(headerHeight, {
              toValue: 1,
              duration: 100,
              useNativeDriver: false,
            }).start();
          }
        }

        lastScrollY.current = currentScrollY;
      },
    }
  );

  const toggleBankSelection = (bank) => {
    setSelectedBanks(prev =>
      prev.includes(bank) ? prev.filter(b => b !== bank) : [...prev, bank]
    );
  };

  const fetchTransactions = useCallback(async () => {
      setRefreshing(true);
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');
      if (!token || !userId) return;

      const res = await axios.post('https://api.pay2s.vn/api/v1/bank', `action=list_transactions&user_id=${userId}`, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`
        }
      });

      if (res.data.status && Array.isArray(res.data.transactions)) {
        let data = res.data.transactions;
        if (tabType !== 'ALL') data = data.filter(tx => tx.type === tabType);
        if (selectedBanks.length > 0) data = data.filter(tx => selectedBanks.includes(tx.shortBankName));
        if (fromDate) data = data.filter(tx => moment(tx.transactionDate).isSameOrAfter(fromDate, 'day'));
        if (toDate) data = data.filter(tx => moment(tx.transactionDate).isSameOrBefore(toDate, 'day'));
        if (searchKeyword.trim() !== '') {
          const keyword = searchKeyword.trim().toLowerCase();
          data = data.filter(tx =>
            tx.description.toLowerCase().includes(keyword) ||
            tx.accountNumber.toLowerCase().includes(keyword)
          );
        }
        const groupedData = {};
        data.forEach(tx => {
          const dateKey = moment(tx.transactionDate).format('DD/MM/YYYY');
          if (!groupedData[dateKey]) groupedData[dateKey] = [];
          groupedData[dateKey].push(tx);
        });

        setGrouped(groupedData);
        calculateStats(data);
      } else {
        setGrouped({});
        calculateStats([]);
      }
    } catch (err) {
      console.error('Transaction error:', err);
    } finally {
      setRefreshing(false);
    }
  }, [tabType, fromDate, toDate, selectedBanks, searchKeyword]);

  const calculateStats = (data) => {
    let total = 0, tin = 0, tout = 0;
    data.forEach(item => {
      const amt = parseFloat(item.amount);
      total += amt;
      if (item.type === 'IN') tin += amt;
      else if (item.type === 'OUT') tout += amt;
    });
    setTotalAmount(total);
    setTotalIn(tin);
    setTotalOut(tout);
  };

  useFocusEffect(useCallback(() => { fetchTransactions(); }, [fetchTransactions]));
  useEffect(() => { fetchTransactions(); }, [fetchTransactions]);

  const isToday = (date) => moment(date, 'DD/MM/YYYY').isSame(moment(), 'day');

  return (
    <PageLayout>
      <TextInput
        placeholder="Tìm kiếm mô tả hoặc STK"
        placeholderTextColor="#aaa"
        value={searchKeyword}
        onChangeText={setSearchKeyword}
        style={[styles.searchBox, { color: '#2c3e50' }]}
      />

      <Animated.View
        style={[
          styles.statsContainer,
          {
            height: headerHeight.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 100],
              extrapolate: 'clamp',
            }),
            opacity: headerHeight.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [0, 0.5, 1],
              extrapolate: 'clamp',
            }),
            marginBottom: headerHeight.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 20],
              extrapolate: 'clamp',
            }),
          }
        ]}
      >
        <View style={styles.statsRow}>
          <View style={[styles.statBox, styles.totalStatBox]}>
            <View style={styles.statContent}>
              <Text style={styles.statTitle}>Tổng GD</Text>
              <Text style={[styles.statValue, styles.totalStatValue]} numberOfLines={2}>
                {`${totalAmount.toLocaleString('vi-VN')} ₫`}
              </Text>
            </View>
          </View>

          <View style={[styles.statBox, styles.inStatBox]}>
            <View style={styles.statContent}>
              <Text style={[styles.statTitle, styles.inStatTitle]}>Tiền vào</Text>
              <Text style={[styles.statValue, styles.inStatValue]} numberOfLines={2}>
                {`+${totalIn.toLocaleString('vi-VN')} ₫`}
              </Text>
            </View>
          </View>

          <View style={[styles.statBox, styles.outStatBox]}>
            <View style={styles.statContent}>
              <Text style={[styles.statTitle, styles.outStatTitle]}>Tiền ra</Text>
              <Text style={[styles.statValue, styles.outStatValue]} numberOfLines={2}>
                {`-${totalOut.toLocaleString('vi-VN')} ₫`}
              </Text>
            </View>
          </View>
        </View>
      </Animated.View>

      <View style={styles.tabsContainer}>
        <View style={styles.tabsWrapper}>
          {TABS.map(tab => (
            <TouchableOpacity
              key={tab.type}
              onPress={() => setTabType(tab.type)}
              style={[styles.tab, tabType === tab.type && styles.activeTab]}
              activeOpacity={0.7}
            >
              <View style={styles.tabContent}>
                <Text style={[styles.tabText, tabType === tab.type && styles.activeTabText]}>
                  {tab.type === 'ALL' ? 'Tất cả' : tab.type === 'IN' ? 'Tiền vào' : 'Tiền ra'}
                </Text>
              </View>
              {tabType === tab.type && <View style={styles.tabIndicator} />}
            </TouchableOpacity>
          ))}
        </View>
        <TouchableOpacity
          onPress={() => setShowFilter(true)}
          style={styles.filterButton}
          activeOpacity={0.7}
        >
          <View style={styles.filterContent}>
            <Text style={styles.filterIcon}>🔍</Text>
            <Text style={styles.filterText}>Lọc</Text>
          </View>
          {(selectedBanks.length > 0 || fromDate || toDate) && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>
                {selectedBanks.length + (fromDate ? 1 : 0) + (toDate ? 1 : 0)}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scroll}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={fetchTransactions} />}
        onScroll={handleScroll}
        scrollEventThrottle={1}
        showsVerticalScrollIndicator={true}
      >
        {Object.keys(grouped).length === 0 ? (
          <Text style={styles.noData}>Không có giao dịch</Text>
        ) : (
          Object.keys(grouped).sort((a, b) => moment(b, 'DD/MM/YYYY').diff(moment(a, 'DD/MM/YYYY'))).map(date => (
            <View key={date}>
              <Text style={[styles.transactionDateLabel, isToday(date) && styles.today]}>{isToday(date) ? 'Hôm nay' : date}</Text>
              {grouped[date].map(tx => (
                <Pressable key={tx.transactionID} style={styles.txItem} onPress={() => setSelectedTransaction(tx)}>
                  <View style={styles.avatarContainer}>
                    <Image
                      source={{ uri: getBankLogo(tx.shortBankName) }}
                      style={styles.avatar}
                      resizeMode="contain"
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.bankName}>{tx.bankName}</Text>
                    <Text style={styles.account}>STK: {tx.accountNumber}</Text>
                    <Text style={styles.desc}>{tx.description}</Text>
                  </View>
                  <View style={{ alignItems: 'flex-end' }}>
                    <Text style={[styles.amount, tx.type === 'IN' ? styles.in : styles.out]}>{tx.type === 'IN' ? '+' : '-'}{parseInt(tx.amount).toLocaleString()} ₫</Text>
                    <Text style={styles.time}>{moment(tx.transactionDate).format('HH:mm:ss')}</Text>
                  </View>
                </Pressable>
              ))}
            </View>
          ))
        )}
      </ScrollView>

      <Modal visible={showFilter} transparent animationType="slide">
        <TouchableWithoutFeedback onPress={() => setShowFilter(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.modal}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>🔍 Bộ lọc nâng cao</Text>
                  <TouchableOpacity onPress={() => setShowFilter(false)} style={styles.closeButton}>
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
                  <View style={styles.filterSection}>
                    <Text style={styles.sectionTitle}>🏦 Chọn ngân hàng</Text>
                    <View style={styles.bankGrid}>
                      {BANKS.map(bank => (
                        <TouchableOpacity
                          key={bank}
                          onPress={() => toggleBankSelection(bank)}
                          style={[
                            styles.bankItem,
                            selectedBanks.includes(bank) && styles.bankItemSelected
                          ]}
                        >
                          <Text style={[
                            styles.bankText,
                            selectedBanks.includes(bank) && styles.bankTextSelected
                          ]}>
                            {bank}
                          </Text>
                          {selectedBanks.includes(bank) && (
                            <Text style={styles.checkIcon}>✓</Text>
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  <View style={styles.filterSection}>
                    <Text style={styles.sectionTitle}>📅 Khoảng thời gian</Text>

                    <View style={styles.dateRow}>
                      <View style={styles.dateColumn}>
                        <Text style={styles.dateLabel}>Từ ngày</Text>
                        <TouchableOpacity
                          onPress={() => setShowDatePicker('from')}
                          style={styles.dateButton}
                        >
                          <Text style={styles.dateButtonText}>
                            {fromDate ? moment(fromDate).format('DD/MM/YYYY') : 'Chọn ngày'}
                          </Text>
                          <Text style={styles.calendarIcon}>📅</Text>
                        </TouchableOpacity>
                      </View>

                      <View style={styles.dateColumn}>
                        <Text style={styles.dateLabel}>Đến ngày</Text>
                        <TouchableOpacity
                          onPress={() => setShowDatePicker('to')}
                          style={styles.dateButton}
                        >
                          <Text style={styles.dateButtonText}>
                            {toDate ? moment(toDate).format('DD/MM/YYYY') : 'Chọn ngày'}
                          </Text>
                          <Text style={styles.calendarIcon}>📅</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </ScrollView>

                {showDatePicker && (
                  <DateTimePicker
                    mode="date"
                    value={showDatePicker === 'from' ? fromDate || new Date() : toDate || new Date()}
                    onChange={(_, selectedDate) => {
                      if (showDatePicker === 'from') setFromDate(selectedDate || null);
                      else setToDate(selectedDate || null);
                      setShowDatePicker(null);
                    }}
                  />
                )}

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    onPress={() => { setFromDate(null); setToDate(null); setSelectedBanks([]); }}
                    style={styles.clearButton}
                  >
                    <Text style={styles.clearButtonText}>🗑️ Xóa bộ lọc</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => { setShowFilter(false); fetchTransactions(); }}
                    style={styles.applyButton}
                  >
                    <Text style={styles.applyButtonText}>✓ Áp dụng</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      <Modal visible={!!selectedTransaction} transparent animationType="fade">
        <TouchableWithoutFeedback onPress={() => setSelectedTransaction(null)}>
          <View style={styles.detailModalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.detailModal}>
                <View style={styles.detailModalHeader}>
                  <View style={styles.detailModalIcon}>
                    <Text style={styles.detailModalIconText}>💳</Text>
                  </View>
                  <Text style={styles.detailModalTitle}>Chi tiết giao dịch</Text>
                  <TouchableOpacity
                    onPress={() => setSelectedTransaction(null)}
                    style={styles.detailCloseButton}
                  >
                    <Text style={styles.detailCloseButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>

                {selectedTransaction && (
                  <View style={styles.detailContent}>
                    <View style={styles.detailBankSection}>
                      <View style={styles.detailBankLogo}>
                        <Image
                          source={{ uri: getBankLogo(selectedTransaction.shortBankName) }}
                          style={styles.detailBankImage}
                          resizeMode="contain"
                        />
                      </View>
                      <View style={styles.detailBankInfo}>
                        <Text style={styles.detailBankName}>{selectedTransaction.bankName}</Text>
                        <Text style={styles.detailAccountNumber}>STK: {selectedTransaction.accountNumber}</Text>
                      </View>
                    </View>

                    <View style={styles.detailAmountSection}>
                      <Text style={styles.detailAmountLabel}>Số tiền giao dịch</Text>
                      <Text style={[
                        styles.detailAmount,
                        selectedTransaction.type === 'IN' ? styles.detailAmountIn : styles.detailAmountOut
                      ]}>
                        {selectedTransaction.type === 'IN' ? '+' : '-'}{parseInt(selectedTransaction.amount).toLocaleString('vi-VN')} ₫
                      </Text>
                      <View style={[
                        styles.detailTypeBadge,
                        selectedTransaction.type === 'IN' ? styles.detailTypeBadgeIn : styles.detailTypeBadgeOut
                      ]}>
                        <Text style={[
                          styles.detailTypeText,
                          selectedTransaction.type === 'IN' ? styles.detailTypeTextIn : styles.detailTypeTextOut
                        ]}>
                          {selectedTransaction.type === 'IN' ? '💰 Tiền vào' : '💸 Tiền ra'}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailInfoSection}>
                      <View style={styles.detailInfoRow}>
                        <Text style={styles.detailInfoLabel}>📝 Nội dung:</Text>
                        <Text style={styles.detailInfoValue}>{selectedTransaction.description}</Text>
                      </View>

                      <View style={styles.detailInfoRow}>
                        <Text style={styles.detailInfoLabel}>🕐 Thời gian:</Text>
                        <Text style={styles.detailInfoValue}>
                          {moment(selectedTransaction.transactionDate).format('DD/MM/YYYY HH:mm:ss')}
                        </Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={() => setSelectedTransaction(null)}
                      style={styles.detailOkButton}
                    >
                      <Text style={styles.detailOkButtonText}>✓ Đóng</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
    searchBox: {
        backgroundColor: '#fff', margin: 12, padding: 10, borderRadius: 8,
        fontSize: 15, borderColor: '#ccc', borderWidth: 1, color: '#000000'
      },
  scroll: { flex: 1, backgroundColor: '#f2f2f2' },
  noData: { textAlign: 'center', marginTop: 30, color: '#aaa' },
  transactionDateLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 10,
    color: '#2c3e50',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
    overflow: 'hidden'
  },
  today: { color: '#e67e22' },
  txItem: {
    backgroundColor: '#fff', marginHorizontal: 12, marginBottom: 10,
    padding: 16, borderRadius: 12, flexDirection: 'row', alignItems: 'center',
    shadowColor: '#000', shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1, shadowRadius: 3, elevation: 2
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  bankName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  account: { color: '#555', fontSize: 13, color: '#2c3e50' },
  desc: { color: '#666', marginTop: 2 , color: '#2c3e50'},
  amount: { fontWeight: 'bold', fontSize: 16 },
  in: { color: '#2ecc71' },
  out: { color: '#e74c3c' },
  time: { fontSize: 12, color: '#999', marginTop: 4 },
  tabsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    zIndex: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  tabsWrapper: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 25,
    padding: 4
  },
  tab: {
    flex: 1,
    marginHorizontal: 2,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 10,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  },
  activeTab: {
    backgroundColor: '#fff',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  tabIcon: {
    fontSize: 16,
    marginBottom: 4
  },
  tabText: {
    fontWeight: '600',
    color: '#6c757d',
    fontSize: 12,
    textAlign: 'center'
  },
  activeTabText: {
    color: '#308a5a',
    fontWeight: 'bold'
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 2,
    left: '25%',
    right: '25%',
    height: 3,
    backgroundColor: '#308a5a',
    borderRadius: 2
  },
  filterButton: {
    marginLeft: 16,
    backgroundColor: '#308a5a',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 5,
    position: 'relative',
    minWidth: 80
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  filterIcon: {
    color: '#fff',
    fontSize: 16,
    marginRight: 6
  },
  filterText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold'
  },
  filterBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#e74c3c',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff'
  },
  filterBadgeText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'flex-end',
    paddingHorizontal: 0
  },
  modal: {
    backgroundColor: 'white',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 30,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0'
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    flex: 1
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center'
  },
  closeButtonText: {
    fontSize: 16,
    color: '#6c757d',
    fontWeight: 'bold'
  },
  modalContent: {
    maxHeight: 400
  },
  filterSection: {
    marginBottom: 25
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15
  },
  bankGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10
  },
  bankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1.5,
    borderColor: '#e9ecef',
    backgroundColor: '#fff',
    minWidth: 80,
    justifyContent: 'center'
  },
  bankItemSelected: {
    backgroundColor: '#308a5a',
    borderColor: '#308a5a'
  },
  bankText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50'
  },
  bankTextSelected: {
    color: '#fff'
  },
  checkIcon: {
    fontSize: 12,
    color: '#fff',
    marginLeft: 6,
    fontWeight: 'bold'
  },
  dateRow: {
    flexDirection: 'row',
    gap: 15
  },
  dateColumn: {
    flex: 1
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6c757d',
    marginBottom: 8
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef'
  },
  dateButtonText: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '500'
  },
  calendarIcon: {
    fontSize: 16
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 25,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0'
  },
  clearButton: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: '#fff',
    borderWidth: 1.5,
    borderColor: '#e74c3c',
    alignItems: 'center'
  },
  clearButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#e74c3c'
  },
  applyButton: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: '#308a5a',
    alignItems: 'center',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3
  },
  applyButtonText: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#fff'
  },
  // Transaction Detail Modal Styles
  detailModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  detailModal: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15
  },
  detailModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0'
  },
  detailModalIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e8f5e9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12
  },
  detailModalIconText: {
    fontSize: 20
  },
  detailModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    flex: 1
  },
  detailCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center'
  },
  detailCloseButtonText: {
    fontSize: 16,
    color: '#6c757d',
    fontWeight: 'bold'
  },
  detailContent: {
    padding: 20
  },
  detailBankSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 12,
    marginBottom: 20
  },
  detailBankLogo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    borderWidth: 1,
    borderColor: '#e9ecef'
  },
  detailBankImage: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  detailBankInfo: {
    flex: 1
  },
  detailBankName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4
  },
  detailAccountNumber: {
    fontSize: 14,
    color: '#6c757d'
  },
  detailAmountSection: {
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
    marginBottom: 20
  },
  detailAmountLabel: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 8
  },
  detailAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 12
  },
  detailAmountIn: {
    color: '#2ecc71'
  },
  detailAmountOut: {
    color: '#e74c3c'
  },
  detailTypeBadge: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20
  },
  detailTypeBadgeIn: {
    backgroundColor: '#e8f5e9'
  },
  detailTypeBadgeOut: {
    backgroundColor: '#fdecea'
  },
  detailTypeText: {
    fontSize: 14,
    fontWeight: '600'
  },
  detailTypeTextIn: {
    color: '#2ecc71'
  },
  detailTypeTextOut: {
    color: '#e74c3c'
  },
  detailInfoSection: {
    marginBottom: 25
  },
  detailInfoRow: {
    marginBottom: 15
  },
  detailInfoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 6
  },
  detailInfoValue: {
    fontSize: 15,
    color: '#2c3e50',
    lineHeight: 22
  },
  detailOkButton: {
    backgroundColor: '#308a5a',
    paddingVertical: 15,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3
  },
  detailOkButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff'
  },
  statsContainer: {
    overflow: 'hidden',
    zIndex: 10,
    elevation: 0,
    backgroundColor: '#ffffff'
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 8
  },
  statBox: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    minHeight: 75,
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  totalStatBox: {
    borderLeftWidth: 4,
    borderLeftColor: '#3498db'
  },
  inStatBox: {
    borderLeftWidth: 4,
    borderLeftColor: '#2ecc71'
  },
  outStatBox: {
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c'
  },
  statIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6
  },
  inIconContainer: {
    backgroundColor: '#e8f5e9'
  },
  outIconContainer: {
    backgroundColor: '#fdecea'
  },
  statIcon: {
    fontSize: 14
  },
  statContent: {
    alignItems: 'center',
    width: '100%',
    flex: 1,
    justifyContent: 'center'
  },
  statTitle: {
    fontSize: 10,
    fontWeight: '600',
    color: '#6c757d',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.3,
    textAlign: 'center'
  },
  inStatTitle: {
    color: '#2ecc71'
  },
  outStatTitle: {
    color: '#e74c3c'
  },
  statValue: {
    fontSize: 13,
    fontWeight: 'bold',
    lineHeight: 18,
    textAlign: 'center',
    marginTop: 2
  },
  totalStatValue: {
    color: '#2c3e50'
  },
  inStatValue: {
    color: '#2ecc71'
  },
  outStatValue: {
    color: '#e74c3c'
  }
});