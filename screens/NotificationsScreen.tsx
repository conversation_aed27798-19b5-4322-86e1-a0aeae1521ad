import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import RenderHtml from 'react-native-render-html';
import { useFocusEffect } from '@react-navigation/native';
import { useNotifications } from '../contexts/NotificationContext';
import PageLayout from '../components/PageLayout';


export default function NotificationsScreen() {
  const { notifications, fetchNotifications, markAsRead } = useNotifications();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  // Tự động tải dữ liệu khi vào trang
  useFocusEffect(
    React.useCallback(() => {
      const loadData = async () => {
        setIsLoading(true);
        await fetchNotifications();
        setIsLoading(false);
      };
      loadData();
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  const filteredNotifications = notifications.filter(n =>
    filter === 'unread' ? n.is_read === 0 :
    filter === 'read' ? n.is_read === 1 :
    true
  );

  return (

    <PageLayout>
      <ScrollView
        style={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.content}>
          {/* Bộ lọc */}
          <View style={styles.filterRow}>
            {[
              { label: 'Tất cả', value: 'all' },
              { label: 'Chưa đọc', value: 'unread' },
              { label: 'Đã đọc', value: 'read' },
            ].map((item) => (
              <TouchableOpacity
                key={item.value}
                onPress={() => setFilter(item.value as any)}
              >
                <Text style={[styles.filterButton, filter === item.value && styles.filterButtonActive]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Loading */}
          {isLoading ? (
            <ActivityIndicator size="large" color="#308a5a" style={{ marginTop: 40 }} />
          ) : filteredNotifications.length === 0 ? (
            <Text style={styles.noNotifications}>Không có thông báo nào</Text>
          ) : (
            filteredNotifications.map((notification) => (
              <TouchableOpacity
                key={notification.id}
                style={[
                  styles.notificationItem,
                  notification.is_read === 0 && styles.unreadNotification,
                ]}
                onPress={() => markAsRead(notification.id)}
              >
                <Text style={styles.notificationTitle}>
                  {notification.title}
                </Text>
                <RenderHtml
                    contentWidth={Dimensions.get('window').width - 40}
                    source={{ html: notification.content }}
                    tagsStyles={{
                      body: { color: '#2c3e50', fontSize: 14, lineHeight: 22 },
                      p: { color: '#2c3e50' },
                      span: { color: '#2c3e50' },
                    }}
                  />
                <Text style={styles.notificationDate}>
                  {notification.created_at}
                </Text>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  noNotifications: {
    textAlign: 'center',
    color: '#888',
    marginTop: 30,
    fontSize: 16,
  },
  notificationItem: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: '#308a5a',
    color: '#2c3e50'
  },
  unreadNotification: {
    backgroundColor: '#f0faf5',
    borderLeftColor: '#1abc9c',
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#2c3e50',
  },
  notificationContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    color: '#2c3e50'
  },
  notificationDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 10,
    textAlign: 'right',
  },
  tabRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabButton: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
    backgroundColor: '#e0f2f1',
  },
  tabButtonActive: {
    backgroundColor: '#308a5a',
  },
  tabText: {
    fontWeight: '600',
    color: '#308a5a',
  },
  tabTextActive: {
    color: '#fff',
  },
  filterRow: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: 12,
      gap: 12,
      flexWrap: 'wrap'
    },
    filterButton: {
      backgroundColor: '#eee',
      paddingVertical: 6,
      paddingHorizontal: 14,
      borderRadius: 20,
      fontSize: 14,
      color: '#555',
    },
    filterButtonActive: {
      backgroundColor: '#308a5a',
      color: '#fff',
      fontWeight: 'bold',
    }
});
