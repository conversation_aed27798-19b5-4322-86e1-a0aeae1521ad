import React, { useEffect, useState } from 'react';
import {
  View, Text, ActivityIndicator,
  StyleSheet, ScrollView, TouchableOpacity, Share,
  Platform, Dimensions, Modal, Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import PageLayout from '../components/PageLayout';
import moment from 'moment';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

// Simple gradient replacement component
const SimpleGradient = ({ colors, style, children }) => (
  <View style={[style, { backgroundColor: colors[0] }]}>
    {children}
  </View>
);

export default function OrderDetailScreen({ route }) {
  const { orderId } = route.params;
  const navigation = useNavigation();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState(order?.status || 'pending');
  const [updating, setUpdating] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);

  // Notification states
  const [notification, setNotification] = useState(null);
  const [showNotification, setShowNotification] = useState(false);
  const notificationOpacity = useState(new Animated.Value(0))[0];

  // Custom notification functions
  const showCustomNotification = (type, title, message, duration = 3000) => {
    setNotification({ type, title, message });
    setShowNotification(true);

    Animated.timing(notificationOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    setTimeout(() => {
      hideNotification();
    }, duration);
  };

  const hideNotification = () => {
    Animated.timing(notificationOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowNotification(false);
      setNotification(null);
    });
  };

  const translateStatus = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return {
          label: 'Đã thanh toán',
          color: '#27ae60',
          bgColor: '#d5f4e6',
          icon: 'checkmark-circle'
        };
      case 'pending':
        return {
          label: 'Đang chờ',
          color: '#f39c12',
          bgColor: '#fef9e7',
          icon: 'time'
        };
      case 'cancelled':
        return {
          label: 'Đã huỷ',
          color: '#e74c3c',
          bgColor: '#fdeaea',
          icon: 'close-circle'
        };
      case 'failed':
        return {
          label: 'Thất bại',
          color: '#c0392b',
          bgColor: '#fdeaea',
          icon: 'alert-circle'
        };
      default:
        return {
          label: status,
          color: '#7f8c8d',
          bgColor: '#f8f9fa',
          icon: 'help-circle'
        };
    }
  };

  const updateInvoiceStatus = async () => {
    if (!order?.invoice_number) {
      showCustomNotification('error', 'Lỗi', 'Không có mã hóa đơn.');
      return;
    }

    const token = await AsyncStorage.getItem('userToken');
    setUpdating(true);

    try {
      const userId = await AsyncStorage.getItem('userId');
      const res = await axios.post(
        'https://api.pay2s.vn/api/v1/invoices',
        `action=edit_bank_invoices&user_id=${userId}&status=${selectedStatus}&invoice_number=${order.invoice_number}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (res.data.status) {
        showCustomNotification('success', 'Thành công', 'Trạng thái hóa đơn đã được cập nhật.');
        setOrder((prev) => ({ ...prev, status: selectedStatus }));
      } else {
        showCustomNotification('error', 'Lỗi', res.data.message || 'Cập nhật thất bại.');
      }
    } catch (err) {
      console.error(err);
      showCustomNotification('error', 'Lỗi', 'Không thể cập nhật trạng thái.');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    const fetchOrderDetail = async () => {
      const token = await AsyncStorage.getItem('userToken');
      try {
        const res = await axios.get(
          `https://payment.pay2s.vn/api/v1/orders/detail?requestId=${orderId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        if (res.data.success) {
          setOrder(res.data.data);
        } else {
          showCustomNotification('error', 'Lỗi', res.data.message || 'Không tìm thấy đơn hàng.');
        }
      } catch (err) {
        console.log('❌ Lỗi:', err);
        showCustomNotification('error', 'Lỗi', 'Không thể tải chi tiết đơn hàng.');
      } finally {
        setLoading(false);
      }
    };
    fetchOrderDetail();
  }, []);

  if (loading) {
    return (
      <PageLayout>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#308a5a" />
          <Text style={styles.loadingText}>Đang tải chi tiết đơn hàng...</Text>
        </View>
      </PageLayout>
    );
  }

  if (!order) {
    return (
      <PageLayout>
        <View style={styles.emptyContainer}>
          <Icon name="document-outline" size={60} color="#bdc3c7" />
          <Text style={styles.emptyText}>Không có dữ liệu</Text>
          <Text style={styles.emptySubText}>Không tìm thấy thông tin đơn hàng</Text>
        </View>
      </PageLayout>
    );
  }

  const statusData = translateStatus(order.status);

  const handleShare = () => {
    if (!order) return;

    const message = `🔔 Thông tin hoá đơn:
• Mã đơn: ${order.order_id}
• Số tiền: ${parseInt(order.amount).toLocaleString()} ₫
• Trạng thái: ${translateStatus(order.status).label}
• Ngày tạo: ${moment(order.created_at).format('DD/MM/YYYY HH:mm')}`;

    Share.share({ message });
  };

  const InfoItem = ({ icon, label, value, valueStyle = {} }) => (
    <View style={styles.infoItem}>
      <View style={styles.infoHeader}>
        <Icon name={icon} size={18} color="#308a5a" />
        <Text style={styles.infoLabel}>{label}</Text>
      </View>
      <Text style={[styles.infoValue, valueStyle]}>{value}</Text>
    </View>
  );

  const StatusBadge = ({ status }) => {
    const statusInfo = translateStatus(status);
    return (
      <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
        <Icon name={statusInfo.icon} size={16} color={statusInfo.color} />
        <Text style={[styles.statusText, { color: statusInfo.color }]}>
          {statusInfo.label}
        </Text>
      </View>
    );
  };

  const handleStatusSelect = (status) => {
    setSelectedStatus(status);
    setShowStatusModal(false);
  };

  const StatusModal = () => {
    const statusOptions = [
      { value: 'pending', label: 'Đang chờ', icon: 'time-outline', color: '#f39c12' },
      { value: 'completed', label: 'Đã thanh toán', icon: 'checkmark-circle-outline', color: '#308a5a' },
      { value: 'cancelled', label: 'Đã huỷ', icon: 'close-circle-outline', color: '#e74c3c' }
    ];

    return (
      <Modal
        visible={showStatusModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowStatusModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Chọn trạng thái</Text>
              <TouchableOpacity
                onPress={() => setShowStatusModal(false)}
                style={styles.modalCloseButton}
              >
                <Icon name="close" size={24} color="#7f8c8d" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              {statusOptions.map((status) => (
                <TouchableOpacity
                  key={status.value}
                  style={[
                    styles.modalStatusOption,
                    selectedStatus === status.value && styles.modalStatusOptionSelected
                  ]}
                  onPress={() => handleStatusSelect(status.value)}
                  activeOpacity={0.7}
                >
                  <View style={styles.modalStatusContent}>
                    <View style={[styles.modalStatusIcon, { backgroundColor: `${status.color}20` }]}>
                      <Icon name={status.icon} size={22} color={status.color} />
                    </View>
                    <Text style={[
                      styles.modalStatusText,
                      selectedStatus === status.value && styles.modalStatusTextSelected
                    ]}>
                      {status.label}
                    </Text>
                    {selectedStatus === status.value && (
                      <Icon name="checkmark" size={20} color="#308a5a" />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  // Custom Notification Component
  const CustomNotification = () => {
    if (!showNotification || !notification) return null;

    const getNotificationConfig = (type) => {
      switch (type) {
        case 'success':
          return {
            backgroundColor: '#d4edda',
            borderColor: '#308a5a',
            iconName: 'checkmark-circle',
            iconColor: '#308a5a',
            titleColor: '#155724',
            messageColor: '#155724'
          };
        case 'error':
          return {
            backgroundColor: '#f8d7da',
            borderColor: '#e74c3c',
            iconName: 'close-circle',
            iconColor: '#e74c3c',
            titleColor: '#721c24',
            messageColor: '#721c24'
          };
        case 'info':
          return {
            backgroundColor: '#d1ecf1',
            borderColor: '#17a2b8',
            iconName: 'information-circle',
            iconColor: '#17a2b8',
            titleColor: '#0c5460',
            messageColor: '#0c5460'
          };
        default:
          return {
            backgroundColor: '#f8f9fa',
            borderColor: '#6c757d',
            iconName: 'information-circle',
            iconColor: '#6c757d',
            titleColor: '#495057',
            messageColor: '#495057'
          };
      }
    };

    const config = getNotificationConfig(notification.type);

    return (
      <Modal
        visible={showNotification}
        transparent={true}
        animationType="none"
        onRequestClose={hideNotification}
      >
        <View style={styles.notificationOverlay}>
          <Animated.View
            style={[
              styles.notificationContainer,
              {
                backgroundColor: config.backgroundColor,
                borderColor: config.borderColor,
                opacity: notificationOpacity,
                transform: [{
                  translateY: notificationOpacity.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-50, 0],
                  }),
                }],
              }
            ]}
          >
            <View style={styles.notificationContent}>
              <View style={styles.notificationIcon}>
                <Icon
                  name={config.iconName}
                  size={24}
                  color={config.iconColor}
                />
              </View>

              <View style={styles.notificationText}>
                <Text style={[styles.notificationTitle, { color: config.titleColor }]}>
                  {notification.title}
                </Text>
                <Text style={[styles.notificationMessage, { color: config.messageColor }]}>
                  {notification.message}
                </Text>
              </View>

              <TouchableOpacity
                style={styles.notificationClose}
                onPress={hideNotification}
              >
                <Icon name="close" size={20} color={config.iconColor} />
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    );
  };

  return (
    <PageLayout>
      <ScrollView
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
      >
        {/* Back Button */}
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Icon name="arrow-back" size={24} color="#308a5a" />
          <Text style={styles.backText}>Quay lại</Text>
        </TouchableOpacity>

        {/* Header Card */}
        <SimpleGradient
          colors={['#308a5a', '#4caf50']}
          style={styles.headerCard}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name="receipt-outline" size={32} color="#fff" />
            </View>
            <Text style={styles.headerTitle}>Chi tiết hoá đơn</Text>
            <Text style={styles.headerSubtitle}>#{order.order_id}</Text>
          </View>
        </SimpleGradient>

        {/* Main Info Card */}
        <View style={styles.mainCard}>
          {/* Amount Section */}
          <View style={styles.amountSection}>
            <Text style={styles.amountLabel}>Số tiền thanh toán</Text>
            <Text style={styles.amountValue}>
              {parseInt(order.amount).toLocaleString()} ₫
            </Text>
          </View>

          {/* Status Section */}
          <View style={styles.statusSection}>
            <Text style={styles.sectionTitle}>Trạng thái đơn hàng</Text>
            <StatusBadge status={order.status} />
          </View>

          {/* Details Section */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Thông tin chi tiết</Text>

            <InfoItem
              icon="card-outline"
              label="Mã đơn hàng"
              value={order.order_id}
              valueStyle={styles.monoText}
            />

            <InfoItem
              icon="document-text-outline"
              label="Số hoá đơn"
              value={order.invoice_number || 'Chưa có'}
              valueStyle={styles.monoText}
            />

            <InfoItem
              icon="time-outline"
              label="Ngày tạo"
              value={moment(order.created_at).format('DD/MM/YYYY HH:mm')}
            />
          </View>

          {/* Status Update Section */}
          <View style={styles.updateSection}>
            <View style={styles.sectionTitleContainer}>
              <Icon name="refresh-outline" size={16} color="#308a5a" />
              <Text style={styles.sectionTitle}>Cập nhật trạng thái</Text>
            </View>

            {/* Status Selector Button */}
            <TouchableOpacity
              style={styles.statusSelectorButton}
              onPress={() => setShowStatusModal(true)}
              activeOpacity={0.7}
            >
              <View style={styles.statusSelectorContent}>
                <View style={styles.statusSelectorLeft}>
                  <Text style={styles.statusSelectorLabel}>Trạng thái hiện tại:</Text>
                  <StatusBadge status={selectedStatus} />
                </View>
                <Icon name="chevron-forward" size={20} color="#308a5a" />
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={updateInvoiceStatus}
              disabled={updating}
              style={[styles.actionButton, styles.updateButton, updating && styles.buttonDisabled]}
              activeOpacity={0.8}
            >
              <View style={styles.buttonContent}>
                {updating ? (
                  <>
                    <ActivityIndicator size="small" color="#fff" />
                    <Text style={styles.buttonText}>Đang cập nhật...</Text>
                  </>
                ) : (
                  <>
                    <Icon name="save-outline" size={18} color="#fff" />
                    <Text style={styles.buttonText}>Cập nhật trạng thái</Text>
                  </>
                )}
              </View>
            </TouchableOpacity>
          </View>

          {/* Compact Share Button */}
          <TouchableOpacity
            style={styles.compactShareButton}
            onPress={handleShare}
            activeOpacity={0.8}
          >
            <Icon name="share-outline" size={20} color="#fff" />
            <Text style={styles.compactShareText}>Chia sẻ</Text>
          </TouchableOpacity>
        </View>

        {/* Status Modal */}
        <StatusModal />

        {/* Custom Notification */}
        <CustomNotification />
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingBottom: 30,
  },

  // Loading & Empty States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#2c3e50',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#2c3e50',
    marginTop: 8,
  },

  // Back Button
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    marginBottom: 16,
  },
  backText: {
    fontSize: 16,
    color: '#2c3e50',
    fontWeight: '600',
    marginLeft: 8,
  },

  // Header Card
  headerCard: {
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    padding: 24,
    alignItems: 'center',
  },
  headerIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },

  // Main Card
  mainCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },

  // Amount Section
  amountSection: {
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 20,
  },
  amountLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '500',
    marginBottom: 8,
  },
  amountValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#308a5a',
  },

  // Status Section
  statusSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginLeft: 8,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },

  // Details Section
  detailsSection: {
    marginBottom: 24,
  },
  infoItem: {
    marginBottom: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '500',
    marginLeft: 8,
  },
  infoValue: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '500',
  },
  monoText: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },

  // Update Section
  updateSection: {
    marginBottom: 20,
  },

  // Status Selector Button
  statusSelectorButton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    marginBottom: 16,
  },
  statusSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusSelectorLeft: {
    flex: 1,
  },
  statusSelectorLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '500',
    marginBottom: 8,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalContent: {
    padding: 20,
  },
  modalStatusOption: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  modalStatusOptionSelected: {
    backgroundColor: '#f8fff9',
    borderColor: '#308a5a',
    borderWidth: 2,
  },
  modalStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalStatusIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  modalStatusText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  modalStatusTextSelected: {
    color: '#000000',
    fontWeight: '600',
  },

  // Buttons
  actionButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  updateButton: {
    backgroundColor: '#308a5a',
  },

  // Compact Share Button
  compactShareButton: {
    backgroundColor: '#308a5a',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    shadowColor: '#308a5a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    marginTop: 8,
  },
  compactShareText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  buttonDisabled: {
    backgroundColor: '#bdc3c7',
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  // Notification Styles
  notificationOverlay: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
  },
  notificationContainer: {
    width: '100%',
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  notificationIcon: {
    marginRight: 12,
  },
  notificationText: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  notificationClose: {
    padding: 4,
    marginLeft: 8,
  },
});

