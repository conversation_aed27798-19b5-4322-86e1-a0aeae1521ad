/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of rnasyncstorage symbols
#ifndef rnasyncstorage_H
#define rnasyncstorage_H

#include <string>
#include <vector>

#include <jsi/jsi.h>

namespace facebook {
namespace react {

class JSI_EXPORT NativeAsyncStorageCxxSpecJSI {
 public:
  NativeAsyncStorageCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);
  virtual ~NativeAsyncStorageCxxSpecJSI();

  virtual void multiGet(
      jsi::Runtime &rt,
      std::vector<std::string> keys,
      std::function<void(std::vector<std::vector<std::string>>)> callback,
      std::function<void(std::string)> errorCallback) = 0;
  virtual void multiSet(
      jsi::Runtime &rt,
      std::vector<std::vector<std::string>> kvPairs,
      std::function<void()> callback,
      std::function<void(std::string)> errorCallback) = 0;
  virtual void multiRemove(
      jsi::Runtime &rt,
      std::vector<std::string> keys,
      std::function<void()> callback,
      std::function<void(std::string)> errorCallback) = 0;
  virtual void getAllKeys(
      jsi::Runtime &rt,
      std::function<void(std::vector<std::string>)> callback,
      std::function<void(std::string)> errorCallback) = 0;
  virtual void clear(
      jsi::Runtime &rt,
      std::function<void()> callback,
      std::function<void(std::string)> errorCallback) = 0;
};

} // namespace react
} // namespace facebook

#endif /* rnasyncstorage_H */
