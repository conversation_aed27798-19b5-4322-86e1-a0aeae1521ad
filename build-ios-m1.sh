#!/bin/bash

echo "🚀 Building iOS for MacBook M1..."

# Set environment for M1
export ARCH=x86_64
export HOMEBREW_PREFIX=/usr/local

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf ios/build
rm -rf ~/Library/Developer/Xcode/DerivedData/Pay2SApp-*

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p ios/build/generated/ios/rnasyncstorage

# Copy codegen files if they exist
if [ -d "ios/build/generated/ios" ]; then
    echo "✅ Codegen files found"
else
    echo "⚠️  Creating codegen files..."
    mkdir -p ios/build/generated/ios/rnasyncstorage
    
    # Create the header file
    cat > ios/build/generated/ios/rnasyncstorage/rnasyncstorage.h << 'EOF'
#ifndef rnasyncstorage_H
#define rnasyncstorage_H

#include <string>
#include <vector>

namespace facebook {
namespace react {

class NativeAsyncStorageCxxSpecJSI {
 public:
  virtual ~NativeAsyncStorageCxxSpecJSI() = default;
};

} // namespace react
} // namespace facebook

#endif /* rnasyncstorage_H */
EOF
fi

# Install pods with Rosetta
echo "📦 Installing pods with Rose<PERSON>..."
cd ios
arch -x86_64 pod install --repo-update

# Go back to root
cd ..

# Build with Rosetta
echo "🔨 Building with Rosetta..."
arch -x86_64 npx react-native run-ios --simulator="iPhone 15"
