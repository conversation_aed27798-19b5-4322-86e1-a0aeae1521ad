// utils/api.js
import axios from 'axios';

const API_BASE = 'https://payment.pay2s.vn/api/v1';

export const createOrder = async ({ partnerCode, amount, orderInfo, requestId, bankId }) => {
  try {
    const res = await axios.post(`${API_BASE}/orders/create`, {
      partnerCode,
      amount,
      orderInfo,
      requestId,
      bankIds: bankId
    });
    return res.data;
  } catch (err) {
    console.error('Lỗi tạo đơn:', err);
    throw err;
  }
};
export const fetchTransactionStats = async (userId: string, token: string) => {
 // const userToken = await AsyncStorage.getItem('userToken');
 // const userId = await AsyncStorage.getItem('userId');
 // if (!userToken || !userId) return;

  const res = await axios.post(
    'https://api.pay2s.vn/api/v1/bank',
    `action=list_transactions&user_id=${userId}`, // body đơn giản
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Bearer ${userToken}`
      }
    }
  );

  return res.data.transactions || [];
};