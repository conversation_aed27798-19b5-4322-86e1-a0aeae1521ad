import Sound from 'react-native-sound';

export const playSound = () => {
  const sound = new Sound(require('../assets/sounds/ting.mp3'), (error) => {
    if (error) {
      console.log('❌ Lỗi load âm thanh:', error);
      return;
    }
    console.log('🔊 Đã load âm thanh');
    sound.play(success => {
      if (success) {
        console.log('✅ Đã phát xong âm thanh');
      } else {
        console.log('❌ Lỗi khi phát âm thanh');
      }
      sound.release();
    });
  });
};
