import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import socket from '../socket';
import PushNotification from 'react-native-push-notification';

export interface NotificationItem {
  id: number;
  title: string;
  content: string;
  is_read: number;
  created_at: string;
}

interface NotificationContextProps {
  notifications: NotificationItem[];
  setNotifications: React.Dispatch<React.SetStateAction<NotificationItem[]>>;
  unreadCount: number;
  setUnreadCount: React.Dispatch<React.SetStateAction<number>>;
  markAsRead: (id: number) => void;
  fetchNotifications: () => void;
  addNotification: (noti: NotificationItem) => void;
}

const NotificationContext = createContext<NotificationContextProps>({} as NotificationContextProps);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  const fetchNotifications = async () => {
    try {
      const userToken = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');
      if (!userToken || !userId) return;

      const data = `action=user_noti&user_id=${encodeURIComponent(userId)}`;
      const res = await axios.post('https://api.pay2s.vn/api/v1/system', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${userToken}`,
        },
      });

      if (res.data.status) {
        const newData: NotificationItem[] = res.data.notification;

        // 👉 Gộp với danh sách cũ và cập nhật đúng unreadCount
        setNotifications(prev => {
          const merged = [...newData];
          prev.forEach(oldItem => {
            if (!merged.find(item => item.id === oldItem.id)) {
              merged.push(oldItem);
            }
          });

          const sorted = merged.sort((a, b) => b.id - a.id);
          const unread = sorted.filter(n => n.is_read === 0).length;
          setUnreadCount(unread);

          return sorted;
        });
      }
    } catch (err) {
      console.error('fetchNotifications error:', err);
    }
  };

  const markAsReadBulk = async () => {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');
      await axios.post(
        'https://api.pay2s.vn/api/v1/notification',
        `action=mark_all_read&user_id=${userId}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (err) {
      console.error('❌ markAllRead error:', err);
    }
  };

  const markAsRead = async (id: number) => {
    try {
      const userToken = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');
      if (!userToken || !userId) return;

      const data = `action=mark_noti_read&id=${id}&user_id=${encodeURIComponent(userId)}`;
      await axios.post('https://api.pay2s.vn/api/v1/user', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${userToken}`,
        },
      });

      setNotifications(prev =>
        prev.map(n => (n.id === id ? { ...n, is_read: 1 } : n))
      );
      setUnreadCount(prev => Math.max(prev - 1, 0));
    } catch (err) {
      console.error('markAsRead error:', err);
    }
  };

  const addNotification = (noti: NotificationItem) => {
    setNotifications(prev => {
      if (prev.find(n => n.id === noti.id)) return prev;
      return [noti, ...prev];
    });
    if (noti.is_read === 0) {
      setUnreadCount(prev => prev + 1);
    }
  };

  useEffect(() => {
    let isMounted = true;
    fetchNotifications();

    AsyncStorage.getItem('userId').then(userId => {
      if (userId) {
        socket.emit('register', userId);
      }
    });

   const onNewTransaction = (data: NotificationItem) => {
     setNotifications(prev => {
       if (prev.some(n => n.id === data.id)) return prev; // tránh trùng
       return [data, ...prev];
     });

     setUnreadCount(prev => prev + 1); // ✅ tăng số chưa đọc

     // 👇 Thông báo cục bộ
       PushNotification.localNotification({
         title: data.title,
         message: data.content,
         playSound: true,
         soundName: 'default',
         importance: 'high',
         vibrate: true,
       });
   };

    socket.on('transaction:new', onNewTransaction);

    return () => {
      isMounted = false;
      socket.off('transaction:new', onNewTransaction);
    };
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        setNotifications,
        unreadCount,
        setUnreadCount,
        markAsRead,
        fetchNotifications,
        addNotification
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => useContext(NotificationContext);
