#!/bin/bash

echo "🔧 Fixing Hermes script phase warning..."

# Find the Pods project file
PODS_PROJECT="ios/Pods/Pods.xcodeproj/project.pbxproj"

if [ -f "$PODS_PROJECT" ]; then
    echo "✅ Found Pods project file"
    
    # Backup the original file
    cp "$PODS_PROJECT" "$PODS_PROJECT.backup"
    
    # Fix the Hermes script phase by setting runOnlyForDeploymentPostprocessing = 1
    sed -i '' 's/runOnlyForDeploymentPostprocessing = 0;/runOnlyForDeploymentPostprocessing = 1;/g' "$PODS_PROJECT"
    
    echo "✅ Fixed Hermes script phase warning"
    echo "📝 Backup created at: $PODS_PROJECT.backup"
else
    echo "❌ Pods project file not found"
fi

echo "🎉 Hermes warning fix completed!"
