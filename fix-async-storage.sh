#!/bin/bash

echo "🔧 Fixing RNCAsyncStorage header paths..."

# Required headers that need to be explicitly added
REQUIRED_HEADERS=(
    "FBLazyVector"
    "RCTRequired"
    "RCTTypeSafety"
    "ReactCommon"
    "React-Core"
)

# Fix debug config
DEBUG_CONFIG="ios/Pods/Target Support Files/RNCAsyncStorage/RNCAsyncStorage.debug.xcconfig"
if [ -f "$DEBUG_CONFIG" ]; then
    echo "✅ Fixing debug config..."

    # Read current header search paths
    CURRENT_PATHS=$(grep "HEADER_SEARCH_PATHS" "$DEBUG_CONFIG")

    # Add missing headers
    for header in "${REQUIRED_HEADERS[@]}"; do
        if ! echo "$CURRENT_PATHS" | grep -q "Headers/Public/$header"; then
            echo "  Adding $header to debug config..."
            sed -i '' "s|Headers/Public\"|Headers/Public\" \"\${PODS_ROOT}/Headers/Public/$header\"|g" "$DEBUG_CONFIG"
        fi
    done

    # Add codegen path if not already present
    if ! grep -q "build/generated/ios" "$DEBUG_CONFIG"; then
        sed -i '' 's|"${PODS_ROOT}/Headers/Public/hermes-engine"|"${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/../build/generated/ios"|g' "$DEBUG_CONFIG"
    fi
fi

# Fix release config
RELEASE_CONFIG="ios/Pods/Target Support Files/RNCAsyncStorage/RNCAsyncStorage.release.xcconfig"
if [ -f "$RELEASE_CONFIG" ]; then
    echo "✅ Fixing release config..."

    # Read current header search paths
    CURRENT_PATHS=$(grep "HEADER_SEARCH_PATHS" "$RELEASE_CONFIG")

    # Add missing headers
    for header in "${REQUIRED_HEADERS[@]}"; do
        if ! echo "$CURRENT_PATHS" | grep -q "Headers/Public/$header"; then
            echo "  Adding $header to release config..."
            sed -i '' "s|Headers/Public\"|Headers/Public\" \"\${PODS_ROOT}/Headers/Public/$header\"|g" "$RELEASE_CONFIG"
        fi
    done

    # Add codegen path if not already present
    if ! grep -q "build/generated/ios" "$RELEASE_CONFIG"; then
        sed -i '' 's|"${PODS_ROOT}/Headers/Public/hermes-engine"|"${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/../build/generated/ios"|g' "$RELEASE_CONFIG"
    fi
fi

# Verify codegen files exist
if [ -f "ios/build/generated/ios/rnasyncstorage/rnasyncstorage.h" ]; then
    echo "✅ Codegen header file exists"
else
    echo "❌ Codegen header file missing - running codegen..."
    cd ios && arch -x86_64 pod install
    cd ..
fi

echo "🎉 RNCAsyncStorage fix completed!"
