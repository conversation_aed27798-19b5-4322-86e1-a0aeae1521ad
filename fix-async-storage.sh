#!/bin/bash

echo "🔧 Fixing RNCAsyncStorage header paths..."

# Fix debug config
DEBUG_CONFIG="ios/Pods/Target Support Files/RNCAsyncStorage/RNCAsyncStorage.debug.xcconfig"
if [ -f "$DEBUG_CONFIG" ]; then
    echo "✅ Fixing debug config..."
    # Add codegen path if not already present
    if ! grep -q "build/generated/ios" "$DEBUG_CONFIG"; then
        sed -i '' 's|"${PODS_ROOT}/Headers/Public/hermes-engine"|"${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/../build/generated/ios"|g' "$DEBUG_CONFIG"
    fi
fi

# Fix release config
RELEASE_CONFIG="ios/Pods/Target Support Files/RNCAsyncStorage/RNCAsyncStorage.release.xcconfig"
if [ -f "$RELEASE_CONFIG" ]; then
    echo "✅ Fixing release config..."
    # Add codegen path if not already present
    if ! grep -q "build/generated/ios" "$RELEASE_CONFIG"; then
        sed -i '' 's|"${PODS_ROOT}/Headers/Public/hermes-engine"|"${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/../build/generated/ios"|g' "$RELEASE_CONFIG"
    fi
fi

# Verify codegen files exist
if [ -f "ios/build/generated/ios/rnasyncstorage/rnasyncstorage.h" ]; then
    echo "✅ Codegen header file exists"
else
    echo "❌ Codegen header file missing - running codegen..."
    cd ios && arch -x86_64 pod install
    cd ..
fi

echo "🎉 RNCAsyncStorage fix completed!"
